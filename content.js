// Content script for Marginalia Chrome Extension
(function() {
  'use strict';

  console.log('🔖 Marginalia content script loaded on:', window.location.href);

  let isPopupOpen = false;
  let currentSelection = null;

  // Initialize immediately
  initializeScript();

  function initializeScript() {
    console.log('🔖 Marginalia: Initializing content script');

    // Listen for text selection - only on mouseup to avoid conflicts
    document.addEventListener('mouseup', handleTextSelection, true);

    // Close popup when clicking outside (but not immediately)
    document.addEventListener('mousedown', (e) => {
      if (isPopupOpen && !e.target.closest('.marginalia-quote-popup')) {
        // Small delay to allow popup interactions
        setTimeout(() => {
          if (isPopupOpen && !e.target.closest('.marginalia-quote-popup')) {
            console.log('Marginalia: Hiding popup - clicked outside');
            hideQuotePopup();
          }
        }, 100);
      }
    }, true);

    // Close popup on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && isPopupOpen) {
        hideQuotePopup();
      }
    }, true);

    console.log('🔖 Marginalia: Event listeners attached');
  }

function handleTextSelection() {
  console.log('Marginalia: Text selection event triggered');

  // Small delay to ensure selection is complete
  setTimeout(() => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    console.log('Marginalia: Selected text:', selectedText);
    console.log('Marginalia: Selection range count:', selection.rangeCount);
    console.log('Marginalia: isPopupOpen:', isPopupOpen);

    if (selectedText.length > 0 && selection.rangeCount > 0 && !isPopupOpen) {
      console.log('Marginalia: Showing quote popup');
      currentSelection = {
        text: selectedText,
        range: selection.getRangeAt(0).cloneRange()
      };
      showQuotePopup(selection);
    }
    // Don't auto-hide popup when selection is lost - let user close it manually
  }, 100);
}

function showQuotePopup(selection) {
  console.log('Marginalia: Creating quote popup');

  // Remove existing popup if any
  hideQuotePopup();

  // Store the current selection to restore it later
  const range = selection.getRangeAt(0).cloneRange();

  const popup = createQuotePopup();

  if (!document.body) {
    console.error('Marginalia: document.body not available');
    return;
  }

  document.body.appendChild(popup);
  console.log('Marginalia: Popup added to DOM');

  // Position popup near selection
  try {
    const rect = range.getBoundingClientRect();
    const popupRect = popup.getBoundingClientRect();

    let top = rect.bottom + window.scrollY + 10;
    let left = rect.left + window.scrollX;

    // Adjust position if popup would go off screen
    if (left + popupRect.width > window.innerWidth) {
      left = window.innerWidth - popupRect.width - 10;
    }

    if (top + popupRect.height > window.innerHeight + window.scrollY) {
      top = rect.top + window.scrollY - popupRect.height - 10;
    }

    popup.style.top = `${top}px`;
    popup.style.left = `${left}px`;

    console.log('Marginalia: Popup positioned at', { top, left });

    isPopupOpen = true;

    // Restore the selection after a brief delay to ensure popup is rendered
    setTimeout(() => {
      restoreSelection(range);
      // Focus on the note input after restoring selection
      const noteInput = popup.querySelector('.marginalia-note-input');
      noteInput.focus();
    }, 50);

  } catch (error) {
    console.error('Marginalia: Error positioning popup:', error);
  }
}

function restoreSelection(range) {
  try {
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
    console.log('Marginalia: Selection restored');
  } catch (error) {
    console.error('Marginalia: Error restoring selection:', error);
  }
}

function createQuotePopup() {
  const popup = document.createElement('div');
  popup.className = 'marginalia-quote-popup';
  popup.innerHTML = `
    <div class="marginalia-popup-header">
      <span class="marginalia-popup-title">Save Quote</span>
      <button class="marginalia-close-btn" type="button">×</button>
    </div>
    <div class="marginalia-popup-content">
      <div class="marginalia-selected-text">
        "${currentSelection.text}"
      </div>
      <textarea 
        class="marginalia-note-input" 
        placeholder="Add your notes here..."
        rows="3"
      ></textarea>
      <div class="marginalia-popup-actions">
        <button class="marginalia-cancel-btn" type="button">Cancel</button>
        <button class="marginalia-save-btn" type="button">Save Quote</button>
      </div>
    </div>
  `;
  
  // Add event listeners
  popup.querySelector('.marginalia-close-btn').addEventListener('click', hideQuotePopup);
  popup.querySelector('.marginalia-cancel-btn').addEventListener('click', hideQuotePopup);
  popup.querySelector('.marginalia-save-btn').addEventListener('click', saveQuote);
  
  // Handle Enter key in textarea (Ctrl+Enter to save)
  popup.querySelector('.marginalia-note-input').addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      saveQuote();
    }
    if (e.key === 'Escape') {
      hideQuotePopup();
    }
  });
  
  // Prevent popup from closing when clicking inside it
  popup.addEventListener('click', (e) => {
    e.stopPropagation();
    // Restore selection after any click inside popup
    setTimeout(() => restoreSelection(currentSelection.range), 10);
  });

  // Prevent popup from closing when interacting with it
  popup.addEventListener('mousedown', (e) => {
    e.stopPropagation();
    // Restore selection after mousedown
    setTimeout(() => restoreSelection(currentSelection.range), 10);
  });

  // Restore selection when focusing on textarea
  popup.querySelector('.marginalia-note-input').addEventListener('focus', () => {
    setTimeout(() => restoreSelection(currentSelection.range), 10);
  });
  
  return popup;
}

function hideQuotePopup(clearSelection = true) {
  const existingPopup = document.querySelector('.marginalia-quote-popup');
  if (existingPopup) {
    existingPopup.remove();
  }
  isPopupOpen = false;

  // Only clear selection if explicitly requested (when user closes popup)
  if (clearSelection) {
    window.getSelection().removeAllRanges();
    console.log('Marginalia: Selection cleared');
  }
}

async function saveQuote() {
  const popup = document.querySelector('.marginalia-quote-popup');
  const noteInput = popup.querySelector('.marginalia-note-input');
  const saveBtn = popup.querySelector('.marginalia-save-btn');
  
  // Show loading state
  saveBtn.textContent = 'Saving...';
  saveBtn.disabled = true;
  
  try {
    const quoteData = {
      text: currentSelection.text,
      note: noteInput.value.trim()
    };
    
    // Send to background script
    const response = await chrome.runtime.sendMessage({
      action: 'saveQuote',
      data: quoteData
    });
    
    if (response.success) {
      // Show success feedback
      saveBtn.textContent = 'Saved!';
      saveBtn.style.backgroundColor = '#10b981';
      
      // Hide popup after short delay and clear selection
      setTimeout(() => {
        hideQuotePopup(true); // true = clear selection
      }, 1000);
    } else {
      throw new Error(response.error || 'Failed to save quote');
    }
  } catch (error) {
    console.error('Error saving quote:', error);
    saveBtn.textContent = 'Error - Try Again';
    saveBtn.style.backgroundColor = '#ef4444';
    saveBtn.disabled = false;
  }
}

})();


